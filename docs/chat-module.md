# Chat Module Documentation

## Overview

The Chat Module provides a comprehensive chat API with OpenAI integration, streaming support, and conversation management. It uses LangChain and OpenAI for AI-powered conversations with full database persistence.

## Features

- 🤖 **AI Chat Integration**: Powered by OpenAI GPT models via LangChain
- 🔄 **Real-time Streaming**: Server-sent events (SSE) for live chat responses
- 💬 **Conversation Management**: Create, list, and manage chat conversations
- 💾 **Message Persistence**: All messages are stored in PostgreSQL database
- 🔐 **Authentication**: JWT-based authentication for user-specific conversations
- 📱 **RESTful API**: Clean REST endpoints with comprehensive documentation

## Setup

### 1. Environment Configuration

Add your OpenAI API key to the `.env` file:

```bash
# OpenAI Configuration
OPENAI_API_KEY=your-openai-api-key-here
OPENAI_MODEL=gpt-3.5-turbo
OPENAI_MAX_TOKENS=1000
OPENAI_TEMPERATURE=0.7
```

### 2. Database Migration

The chat tables are automatically created when you run:

```bash
uv run alembic upgrade head
```

### 3. Dependencies

The required dependencies are automatically installed:

```bash
uv sync --dev
```

## API Endpoints

### Authentication

All chat endpoints require authentication. Include the JWT token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

### 1. Conversation Management

#### Create Conversation

```http
POST /api/v1/chat/conversations?title=My Chat Session
```

**Response:**

```json
{
  "id": 1,
  "title": "My Chat Session",
  "created_at": "2024-01-15T10:30:00Z",
  "message": "Conversation created successfully"
}
```

#### List Conversations

```http
GET /api/v1/chat/conversations
```

**Response:**

```json
[
  {
    "id": 1,
    "title": "My Chat Session",
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-15T10:35:00Z"
  }
]
```

#### Delete Conversation

```http
DELETE /api/v1/chat/conversations/{conversation_id}
```

### 2. Chat Messages

#### Send Message (Non-streaming)

```http
POST /api/v1/chat/conversations/{conversation_id}/messages
```

**Request Body:**

```json
{
  "messages": [
    {
      "role": "user",
      "content": "Hello! How are you?"
    }
  ],
  "model": "gpt-3.5-turbo",
  "temperature": 0.7,
  "max_tokens": 1000,
  "stream": false
}
```

**Response:**

```json
{
  "message": "Hello! I'm doing well, thank you for asking. How can I help you today?",
  "model": "gpt-3.5-turbo",
  "usage": {
    "prompt_tokens": 10,
    "completion_tokens": 15,
    "total_tokens": 25
  }
}
```

#### Send Message (Streaming)

```http
POST /api/v1/chat/conversations/{conversation_id}/stream
```

**Request Body:**

```json
{
  "messages": [
    {
      "role": "user",
      "content": "Tell me a story"
    }
  ],
  "model": "gpt-3.5-turbo",
  "temperature": 0.7,
  "stream": true
}
```

**Response (Server-Sent Events):**

```
data: {"content": "Once", "finish_reason": null, "model": "gpt-3.5-turbo"}

data: {"content": " upon", "finish_reason": null, "model": "gpt-3.5-turbo"}

data: {"content": " a", "finish_reason": null, "model": "gpt-3.5-turbo"}

data: {"content": " time", "finish_reason": null, "model": "gpt-3.5-turbo"}

data: {"content": "", "finish_reason": "stop", "model": "gpt-3.5-turbo"}
```

#### Get Conversation Messages

```http
GET /api/v1/chat/conversations/{conversation_id}/messages?limit=50&offset=0
```

**Response:**

```json
{
  "messages": [
    {
      "role": "user",
      "content": "Hello!"
    },
    {
      "role": "assistant",
      "content": "Hi there! How can I help you?"
    }
  ],
  "total": 2,
  "has_more": false
}
```

### 3. Direct Streaming (No Database)

#### Stream Chat Response

```http
POST /api/v1/chat/stream
```

This endpoint streams responses without saving to the database, useful for testing or temporary conversations.

## Usage Examples

### JavaScript/TypeScript Client

```javascript
// Create a conversation
const createConversation = async (title) => {
  const response = await fetch(
    "/api/v1/chat/conversations?title=" + encodeURIComponent(title),
    {
      method: "POST",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
    }
  );
  return response.json();
};

// Stream a message
const streamMessage = async (conversationId, message) => {
  const response = await fetch(
    `/api/v1/chat/conversations/${conversationId}/stream`,
    {
      method: "POST",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        messages: [{ role: "user", content: message }],
        model: "gpt-3.5-turbo",
        temperature: 0.7,
        stream: true,
      }),
    }
  );

  const reader = response.body.getReader();
  const decoder = new TextDecoder();

  while (true) {
    const { done, value } = await reader.read();
    if (done) break;

    const chunk = decoder.decode(value);
    const lines = chunk.split("\n");

    for (const line of lines) {
      if (line.startsWith("data: ")) {
        try {
          const data = JSON.parse(line.slice(6));
          if (data.content) {
            console.log(data.content); // Handle streaming content
          }
          if (data.finish_reason === "stop") {
            console.log("Stream completed");
            break;
          }
        } catch (e) {
          // Ignore parsing errors
        }
      }
    }
  }
};
```

### Python Client

```python
import httpx
import json

async def stream_chat(conversation_id, message, token):
    async with httpx.AsyncClient() as client:
        async with client.stream(
            "POST",
            f"/api/v1/chat/conversations/{conversation_id}/stream",
            headers={
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json"
            },
            json={
                "messages": [{"role": "user", "content": message}],
                "model": "gpt-3.5-turbo",
                "temperature": 0.7,
                "stream": True
            }
        ) as response:
            async for line in response.aiter_lines():
                if line.startswith("data: "):
                    try:
                        data = json.loads(line[6:])
                        if data.get("content"):
                            print(data["content"], end="", flush=True)
                        if data.get("finish_reason") == "stop":
                            print("\nStream completed")
                            break
                    except json.JSONDecodeError:
                        continue
```

## Configuration

### OpenAI Models

Supported models:

- `gpt-3.5-turbo` (default)
- `gpt-4`
- `gpt-4-turbo-preview`
- `gpt-3.5-turbo-16k`

### Parameters

- **temperature**: Controls randomness (0.0 = deterministic, 2.0 = very random)
- **max_tokens**: Maximum tokens in the response (1-4000)
- **stream**: Whether to stream the response (true/false)

## Error Handling

The API returns appropriate HTTP status codes:

- `200`: Success
- `400`: Bad request (invalid parameters)
- `401`: Unauthorized (invalid/missing token)
- `404`: Conversation not found
- `500`: Internal server error

## Security

- All endpoints require JWT authentication
- Users can only access their own conversations
- Input validation prevents injection attacks
- Rate limiting can be configured

## Testing

Run the test script to verify functionality:

```bash
# First create an admin user
uv run python command.py create-admin

# Then run the test
uv run python test_chat.py
```

## Database Schema

### ChatConversation

- `id`: Primary key
- `user_id`: Foreign key to User table
- `title`: Conversation title
- `created_at`: Creation timestamp
- `updated_at`: Last update timestamp
- `is_active`: Soft delete flag

### ChatMessage

- `id`: Primary key
- `conversation_id`: Foreign key to ChatConversation
- `role`: Message role (user/assistant)
- `content`: Message content
- `model`: AI model used (for assistant messages)
- `tokens_used`: Token count (for assistant messages)
- `created_at`: Creation timestamp

## Performance Considerations

- Streaming responses reduce perceived latency
- Database queries are optimized with proper indexing
- Redis can be used for caching frequent responses
- Consider implementing message pagination for long conversations

## Troubleshooting

### Common Issues

1. **OpenAI API Key Error**

   - Ensure `OPENAI_API_KEY` is set in `.env`
   - Verify the API key is valid and has sufficient credits

2. **Database Connection Error**

   - Check PostgreSQL is running
   - Verify database credentials in `.env`

3. **Authentication Error**

   - Ensure JWT token is valid and not expired
   - Check token format: `Bearer <token>`

4. **Streaming Issues**
   - Verify client supports Server-Sent Events
   - Check network connectivity and proxy settings

### Debug Mode

Enable debug logging in `.env`:

```bash
DEBUG=true
LOG_LEVEL=DEBUG
```

## Future Enhancements

- [ ] Message threading and replies
- [ ] File upload support
- [ ] Voice message support
- [ ] Multi-language support
- [ ] Conversation templates
- [ ] Advanced analytics and metrics
- [ ] WebSocket support for real-time chat
- [ ] Message search and filtering
- [ ] Conversation export functionality
