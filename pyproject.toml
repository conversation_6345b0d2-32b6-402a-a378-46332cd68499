[project]
name = "beco-madcrow"
dynamic = ["version"]
description = "beco madcrow"
readme = "README.md"
requires-python = ">=3.12"

dependencies = [
    "bcrypt>=4.3.0",
    "blinker>=1.9.0",
    "click>=8.2.1",
    "langchain-community>=0.3.27",
    "psutil>=7.0.0",
    "pydantic>=2.0.0",
    "pydantic-settings>=2.10.0",
    "pyjwt>=2.10.1",
    "python-dotenv>=1.0.0",
    "pytz>=2025.2",
    "redis>=6.2.0",
    "uvicorn[standard]>=0.24.0",
]
# Before adding new dependency, consider place it in
# alphabet order (a-z) and suitable group.

[tool.setuptools.dynamic]
version = {attr = "src.__version__"}

[tool.uv]
default-groups = ["api", "ai", "db"]
package = false

[dependency-groups]
############################################################
# [ Dev ] dependency group
# Required for development and running tests
############################################################
dev = [
    "coverage~=7.2.4",
    "dotenv-linter~=0.5.0",
    "faker~=32.1.0",
    "lxml-stubs~=0.5.1",
    "mypy~=1.16.0",
    "ruff~=0.11.5",
    "pytest~=8.3.2",
    "pytest-benchmark~=4.0.0",
    "pytest-cov~=4.1.0",
    "pytest-env~=1.1.3",
    "pytest-mock~=3.14.0",
    "types-aiofiles~=24.1.0",
    "types-beautifulsoup4~=4.12.0",
    "types-cachetools~=5.5.0",
    "types-colorama~=0.4.15",
    "types-defusedxml~=0.7.0",
    "types-deprecated~=1.2.15",
    "types-docutils~=0.21.0",
    "types-jsonschema~=4.23.0",
    "types-flask-cors~=5.0.0",
    "types-flask-migrate~=4.1.0",
    "types-gevent~=24.11.0",
    "types-greenlet~=3.1.0",
    "types-html5lib~=1.1.11",
    "types-markdown~=3.7.0",
    "types-oauthlib~=3.2.0",
    "types-objgraph~=3.6.0",
    "types-olefile~=0.47.0",
    "types-openpyxl~=3.1.5",
    "types-pexpect~=4.9.0",
    "types-protobuf~=5.29.1",
    "types-psutil~=7.0.0",
    "types-psycopg2~=2.9.21",
    "types-pygments~=2.19.0",
    "types-pymysql~=1.1.0",
    "types-python-dateutil~=2.9.0",
    "types-pywin32~=310.0.0",
    "types-pyyaml~=6.0.12",
    "types-regex~=2024.11.6",
    "types-requests~=2.32.0",
    "types-requests-oauthlib~=2.0.0",
    "types-shapely~=2.0.0",
    "types-simplejson>=3.20.0",
    "types-six>=1.17.0",
    "types-tensorflow>=2.18.0",
    "types-tqdm>=4.67.0",
    "types-ujson>=5.10.0",
    "boto3-stubs>=1.38.20",
    "types-jmespath>=1.0.2.20240106",
    "types_pyOpenSSL>=24.1.0",
    "types_cffi>=1.17.0",
    "types_setuptools>=80.9.0",
    "pandas-stubs~=2.2.3",
    "scipy-stubs>=********",
    "pre-commit>=4.2.0",
    "pip-audit>=2.9.0",
    "bandit[toml]>=1.8.0",
    "httpx>=0.28.1",
    "safety>=3.2.4",
    "detect-secrets>=1.5.0",
    "isort>=6.0.1",
    "flake8>=7.3.0",
    "flake8-bugbear>=24.12.12",
    "flake8-comprehensions>=3.16.0",
    "flake8-simplify>=0.22.0",
    "types-pytz>=2025.2.0.20250516",
    "pytest-asyncio>=1.1.0",
]

############################################################
# API dependencies
############################################################
api = [
    "fastapi[standard]>=0.104.0",
    "redis>=5.0.0",
]

############################################################
# AI dependencies
############################################################
ai = [
    "langchain>=0.2.0",
    "langchain-openai>=0.1.0",
    "langchain-core>=0.2.0",
    "openai>=1.50.0",
    "tiktoken>=0.6.0",
]

############################################################
# DB dependencies
############################################################
db = [
    "alembic>=1.16.3",
    "psycopg-binary>=3.2.9",
    "psycopg[binary]>=3.2.9",
    "sqlmodel>=0.0.24",
]

############################################################
# Flake8 Configuration is in .flake8 file
############################################################

[tool.bandit]
exclude_dirs = ["tests", "test", "tests_*", "test_*"]
skips = ["B101", "B601"]
targets = ["src"]
recursive = true

[tool.ruff]
target-version = "py312"
line-length = 120

[tool.ruff.lint]
select = ["E", "F", "I", "B", "UP", "C4", "SIM"]
ignore = [
    "B008",  # Do not perform function call in argument defaults (FastAPI Depends)
]

[tool.ruff.lint.isort]
known-first-party = ["src"]

[tool.mypy]
files = ["src"]
