"""update chat entities with UUID user_id

Revision ID: 047cadcce4d5
Revises: f0a8776b1355
Create Date: 2025-07-25 17:06:44.822518

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
import sqlmodel


# revision identifiers, used by Alembic.
revision: str = '047cadcce4d5'
down_revision: Union[str, Sequence[str], None] = 'f0a8776b1355'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
