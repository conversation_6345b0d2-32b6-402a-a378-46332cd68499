"""Chat entities for database models."""

from datetime import datetime
from typing import Optional
from uuid import UUID
from sqlmodel import Field, SQLModel


class ChatConversation(SQLModel, table=True):
    """Chat conversation entity."""
    __tablename__ = "chat_conversations"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    user_id: UUID = Field(..., description="User ID who owns this conversation")
    title: str = Field(..., description="Conversation title")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="Creation timestamp")
    updated_at: datetime = Field(default_factory=datetime.utcnow, description="Last update timestamp")
    is_active: bool = Field(default=True, description="Whether conversation is active")


class ChatMessage(SQLModel, table=True):
    """Chat message entity."""
    __tablename__ = "chat_messages"
    
    id: Optional[int] = Field(default=None, primary_key=True)
    conversation_id: int = Field(..., foreign_key="chat_conversations.id", description="Conversation ID")
    role: str = Field(..., description="Role of the message sender (user/assistant)")
    content: str = Field(..., description="Content of the message")
    model: Optional[str] = Field(default=None, description="Model used for generation")
    tokens_used: Optional[int] = Field(default=None, description="Number of tokens used")
    created_at: datetime = Field(default_factory=datetime.utcnow, description="Creation timestamp") 