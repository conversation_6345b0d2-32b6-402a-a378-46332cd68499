"""
Chat service for AI chat functionality with conversation management.
"""

import json
import logging
from typing import As<PERSON><PERSON>enerator, List, Optional
from uuid import UUID

from langchain_community.chat_models import ChatOpenAI
from langchain_core.messages import HumanMessage, SystemMessage
from sqlmodel import Session, select

from src.configs import madcrow_config
from src.entities.chat import Chat<PERSON>onversation, ChatMessage as ChatMessageEntity
from src.models.chat import ChatMessage, ChatStreamResponse

logger = logging.getLogger(__name__)


class ChatService:
    """Service for managing chat conversations and AI responses."""

    def __init__(self, db_session: Session):
        """Initialize chat service with database session."""
        self.db_session = db_session
        self.openai_client = ChatOpenAI(
            model=madcrow_config.OPENAI_MODEL,
            temperature=madcrow_config.OPENAI_TEMPERATURE,
            max_tokens=madcrow_config.OPENAI_MAX_TOKENS,
            openai_api_key=madcrow_config.OPENAI_API_KEY,
            streaming=True
        )

    async def create_conversation(self, user_id: UUID, title: str) -> ChatConversation:
        """Create a new chat conversation."""
        try:
            conversation = ChatConversation(
                user_id=user_id,
                title=title,
                is_active=True
            )
            self.db_session.add(conversation)
            self.db_session.commit()
            self.db_session.refresh(conversation)
            
            logger.info(f"Created conversation {conversation.id} for user {user_id}")
            return conversation
            
        except Exception as e:
            logger.exception(f"Error creating conversation: {e}")
            self.db_session.rollback()
            raise

    async def get_conversation(self, chat_id: UUID, user_id: UUID) -> Optional[ChatConversation]:
        """Get a conversation by ID, ensuring user ownership."""
        try:
            statement = select(ChatConversation).where(
                ChatConversation.id == chat_id,
                ChatConversation.user_id == user_id,
                ChatConversation.is_active == True
            )
            return self.db_session.exec(statement).first()
            
        except Exception as e:
            logger.exception(f"Error getting conversation: {e}")
            raise

    async def get_user_conversations(
        self, 
        user_id: UUID, 
        limit: int = 20, 
        offset: int = 0
    ) -> List[dict]:
        """Get all conversations for a user with pagination."""
        try:
            statement = select(ChatConversation).where(
                ChatConversation.user_id == user_id,
                ChatConversation.is_active == True
            ).order_by(ChatConversation.updated_at.desc()).offset(offset).limit(limit)
            
            conversations = self.db_session.exec(statement).all()
            
            return [
                {
                    "id": conv.id,
                    "title": conv.title,
                    "created_at": conv.created_at.isoformat(),
                    "updated_at": conv.updated_at.isoformat(),
                    "message_count": self._get_message_count(conv.id)
                }
                for conv in conversations
            ]
            
        except Exception as e:
            logger.exception(f"Error getting user conversations: {e}")
            raise

    async def get_conversation_messages(self, chat_id: UUID) -> List[ChatMessage]:
        """Get all messages for a conversation."""
        try:
            statement = select(ChatMessageEntity).where(
                ChatMessageEntity.conversation_id == chat_id
            ).order_by(ChatMessageEntity.created_at.asc())
            
            messages = self.db_session.exec(statement).all()
            
            return [
                ChatMessage(
                    role=msg.role,
                    content=msg.content
                )
                for msg in messages
            ]
            
        except Exception as e:
            logger.exception(f"Error getting conversation messages: {e}")
            raise

    async def add_message(
        self, 
        conversation_id: UUID, 
        user_id: UUID, 
        role: str, 
        content: str,
        model: Optional[str] = None
    ) -> ChatMessageEntity:
        """Add a message to a conversation."""
        try:
            message = ChatMessageEntity(
                conversation_id=conversation_id,
                role=role,
                content=content,
                model=model
            )
            self.db_session.add(message)
            self.db_session.commit()
            self.db_session.refresh(message)
            
            # Update conversation timestamp
            conversation = await self.get_conversation(conversation_id, user_id)
            if conversation:
                conversation.updated_at = message.created_at
                self.db_session.add(conversation)
                self.db_session.commit()
            
            return message
            
        except Exception as e:
            logger.exception(f"Error adding message: {e}")
            self.db_session.rollback()
            raise

    async def update_conversation_title(self, chat_id: UUID, title: str) -> ChatConversation:
        """Update the title of a conversation."""
        try:
            statement = select(ChatConversation).where(ChatConversation.id == chat_id)
            conversation = self.db_session.exec(statement).first()
            
            if not conversation:
                raise ValueError("Conversation not found")
            
            conversation.title = title
            self.db_session.add(conversation)
            self.db_session.commit()
            self.db_session.refresh(conversation)
            
            return conversation
            
        except Exception as e:
            logger.exception(f"Error updating conversation title: {e}")
            self.db_session.rollback()
            raise

    async def delete_conversation(self, chat_id: UUID) -> None:
        """Delete a conversation and all its messages."""
        try:
            # Delete all messages first
            messages_statement = select(ChatMessageEntity).where(
                ChatMessageEntity.conversation_id == chat_id
            )
            messages = self.db_session.exec(messages_statement).all()
            
            for message in messages:
                self.db_session.delete(message)
            
            # Delete conversation
            conv_statement = select(ChatConversation).where(ChatConversation.id == chat_id)
            conversation = self.db_session.exec(conv_statement).first()
            
            if conversation:
                self.db_session.delete(conversation)
            
            self.db_session.commit()
            
        except Exception as e:
            logger.exception(f"Error deleting conversation: {e}")
            self.db_session.rollback()
            raise

    async def generate_response(
        self,
        conversation_id: UUID,
        user_id: UUID,
        messages: List[ChatMessage],
        model: str = "gpt-3.5-turbo",
        temperature: float = 0.7,
        max_tokens: int = 1000
    ) -> str:
        """Generate a non-streaming AI response."""
        try:
            # Convert messages to LangChain format
            langchain_messages = []
            for msg in messages:
                if msg.role == "user":
                    langchain_messages.append(HumanMessage(content=msg.content))
                elif msg.role == "assistant":
                    langchain_messages.append(SystemMessage(content=msg.content))

            # Generate response
            response = self.openai_client.invoke(langchain_messages)
            response_content = response.content

            # Save AI response to database
            await self.add_message(
                conversation_id=conversation_id,
                user_id=user_id,
                role="assistant",
                content=response_content,
                model=model
            )

            return response_content

        except Exception as e:
            logger.exception(f"Error generating response: {e}")
            raise

    async def stream_response(
        self,
        conversation_id: UUID,
        user_id: UUID,
        messages: List[ChatMessage],
        model: str = "gpt-3.5-turbo",
        temperature: float = 0.7,
        max_tokens: int = 1000
    ) -> AsyncGenerator[str, None]:
        """Generate a streaming AI response."""
        try:
            # Convert messages to LangChain format
            langchain_messages = []
            for msg in messages:
                if msg.role == "user":
                    langchain_messages.append(HumanMessage(content=msg.content))
                elif msg.role == "assistant":
                    langchain_messages.append(SystemMessage(content=msg.content))

            full_response = ""
            
            # Stream the response
            async for chunk in self.openai_client.astream(langchain_messages):
                if chunk.content:
                    full_response += chunk.content
                    response_chunk = ChatStreamResponse(
                        content=chunk.content,
                        finish_reason=None,
                        model=model
                    )
                    yield f"data: {response_chunk.model_dump_json()}\n\n"

            # Save complete AI response to database
            await self.add_message(
                conversation_id=conversation_id,
                user_id=user_id,
                role="assistant",
                content=full_response,
                model=model
            )

            # Send end signal
            end_chunk = ChatStreamResponse(
                content="",
                finish_reason="stop",
                model=model
            )
            yield f"data: {end_chunk.model_dump_json()}\n\n"

        except Exception as e:
            logger.exception(f"Error streaming response: {e}")
            # Send error signal
            error_chunk = ChatStreamResponse(
                content="",
                finish_reason="error",
                model=model
            )
            yield f"data: {error_chunk.model_dump_json()}\n\n"

    def _get_message_count(self, conversation_id: UUID) -> int:
        """Get the number of messages in a conversation."""
        try:
            statement = select(ChatMessageEntity).where(
                ChatMessageEntity.conversation_id == conversation_id
            )
            messages = self.db_session.exec(statement).all()
            return len(messages)
        except Exception:
            return 0 