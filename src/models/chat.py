"""
Chat models for request and response schemas.
"""

from datetime import datetime
from typing import List, Optional
from uuid import UUID

from pydantic import BaseModel, Field


class ChatMessage(BaseModel):
    """Chat message model."""
    role: str = Field(..., description="Message role (user/assistant)")
    content: str = Field(..., description="Message content")


class ChatRequest(BaseModel):
    """Chat request model."""
    messages: List[ChatMessage] = Field(..., description="List of chat messages")
    model: str = Field(default="gpt-3.5-turbo", description="AI model to use")
    temperature: float = Field(default=0.7, ge=0.0, le=2.0, description="Response randomness")
    max_tokens: int = Field(default=1000, ge=1, le=4000, description="Maximum tokens in response")


class ChatResponse(BaseModel):
    """Chat response model."""
    message: str = Field(..., description="AI response message")
    model: str = Field(..., description="Model used for response")


class ChatStreamResponse(BaseModel):
    """Streaming chat response model."""
    content: str = Field(..., description="Streamed content chunk")
    finish_reason: Optional[str] = Field(None, description="Reason for stream end")
    model: str = Field(..., description="Model used for response")


class ChatHistoryRequest(BaseModel):
    """Chat history request model."""
    limit: int = Field(default=50, ge=1, le=100, description="Number of messages to return")
    offset: int = Field(default=0, ge=0, description="Number of messages to skip")


class ChatHistoryResponse(BaseModel):
    """Chat history response model."""
    chat_id: UUID = Field(..., description="Chat conversation ID")
    title: str = Field(..., description="Chat conversation title")
    messages: List[ChatMessage] = Field(..., description="List of chat messages")
    total_messages: int = Field(..., description="Total number of messages")


class ChatListResponse(BaseModel):
    """Chat list response model."""
    conversations: List[dict] = Field(..., description="List of conversations")
    total: int = Field(..., description="Total number of conversations")
    limit: int = Field(..., description="Number of conversations returned")
    offset: int = Field(..., description="Number of conversations skipped")


class ChatUpdateRequest(BaseModel):
    """Chat update request model."""
    title: str = Field(..., min_length=1, max_length=200, description="New chat title")


class ChatCreateResponse(BaseModel):
    """Chat create response model."""
    chat_id: UUID = Field(..., description="Chat conversation ID")
    message: str = Field(..., description="AI response message")
    success: bool = Field(..., description="Operation success status") 