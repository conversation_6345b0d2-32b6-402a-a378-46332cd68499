"""
Chat routes for FastAPI application.
Provides endpoints for AI chat functionality with conversation management.
"""

import logging
from typing import Optional
from uuid import UUID

from fastapi import Depends, HTTPException, Query, status
from fastapi.responses import StreamingResponse
from sqlmodel import Session

from src.dependencies.auth import get_current_user_from_jwt_required
from src.dependencies.db import get_db_session
from src.entities.account import Account
from src.models.chat import (
    ChatMessage,
    ChatRequest,
    ChatResponse,
    ChatStreamResponse,
    ChatHistoryRequest,
    ChatHistoryResponse,
    ChatListResponse,
    ChatUpdateRequest,
    ChatCreateResponse,
)
from src.services.chat_service import ChatService
from src.routes.base_router import BaseRouter

logger = logging.getLogger(__name__)

router = BaseRouter(prefix="/chat", tags=["Chat"])


@router.post("/", response_model=ChatCreateResponse)
async def create_chat(
    request: ChatRequest,
    current_user: Account = Depends(get_current_user_from_jwt_required),
    db_session: Session = Depends(get_db_session),
    stream: bool = Query(False, description="Enable streaming response"),
    chat_id: Optional[UUID] = Query(None, description="Existing chat ID (optional)"),
) -> ChatCreateResponse:
    """
    Create a new chat or continue existing chat.
    
    If chat_id is not provided, creates a new chat.
    If stream=True, returns streaming response.
    """
    try:
        chat_service = ChatService(db_session)
        
        # If no chat_id provided, create new chat
        if not chat_id:
            # Create new conversation with first message as title
            conversation = await chat_service.create_conversation(
                user_id=current_user.id,
                title=request.messages[0].content[:50] + "..." if len(request.messages[0].content) > 50 else request.messages[0].content
            )
            chat_id = conversation.id
            logger.info(f"Created new chat {chat_id} for user {current_user.id}")
        
        # Add message to conversation
        message = await chat_service.add_message(
            conversation_id=chat_id,
            user_id=current_user.id,
            role="user",
            content=request.messages[0].content
        )
        
        # Generate AI response
        if stream:
            # Return streaming response
            return StreamingResponse(
                chat_service.stream_response(
                    conversation_id=chat_id,
                    user_id=current_user.id,
                    messages=request.messages,
                    model=request.model,
                    temperature=request.temperature,
                    max_tokens=request.max_tokens
                ),
                media_type="text/plain",
                headers={"Cache-Control": "no-cache", "Connection": "keep-alive"}
            )
        else:
            # Return regular response
            ai_response = await chat_service.generate_response(
                conversation_id=chat_id,
                user_id=current_user.id,
                messages=request.messages,
                model=request.model,
                temperature=request.temperature,
                max_tokens=request.max_tokens
            )
            
            return ChatCreateResponse(
                chat_id=chat_id,
                message=ai_response,
                success=True
            )
            
    except Exception as e:
        logger.exception(f"Error in chat endpoint: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Chat error: {str(e)}"
        )


@router.get("/conversations", response_model=ChatListResponse)
async def list_conversations(
    current_user: Account = Depends(get_current_user_from_jwt_required),
    db_session: Session = Depends(get_db_session),
    limit: int = Query(20, ge=1, le=100, description="Number of conversations to return"),
    offset: int = Query(0, ge=0, description="Number of conversations to skip"),
) -> ChatListResponse:
    """
    List all conversations for the current user.
    """
    try:
        chat_service = ChatService(db_session)
        conversations = await chat_service.get_user_conversations(
            user_id=current_user.id,
            limit=limit,
            offset=offset
        )
        
        return ChatListResponse(
            conversations=conversations,
            total=len(conversations),
            limit=limit,
            offset=offset
        )
        
    except Exception as e:
        logger.exception(f"Error listing conversations: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list conversations: {str(e)}"
        )


@router.get("/conversations/{chat_id}/history", response_model=ChatHistoryResponse)
async def get_chat_history(
    chat_id: UUID,
    current_user: Account = Depends(get_current_user_from_jwt_required),
    db_session: Session = Depends(get_db_session),
) -> ChatHistoryResponse:
    """
    Get complete chat history for a specific conversation.
    """
    try:
        chat_service = ChatService(db_session)
        
        # Verify user owns this conversation
        conversation = await chat_service.get_conversation(chat_id, current_user.id)
        if not conversation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Conversation not found"
            )
        
        # Get all messages for this conversation
        messages = await chat_service.get_conversation_messages(chat_id)
        
        return ChatHistoryResponse(
            chat_id=chat_id,
            title=conversation.title,
            messages=messages,
            total_messages=len(messages)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error getting chat history: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get chat history: {str(e)}"
        )


@router.put("/conversations/{chat_id}/title")
async def update_chat_title(
    chat_id: UUID,
    request: ChatUpdateRequest,
    current_user: Account = Depends(get_current_user_from_jwt_required),
    db_session: Session = Depends(get_db_session),
):
    """
    Update the title of a chat conversation.
    """
    try:
        chat_service = ChatService(db_session)
        
        # Verify user owns this conversation
        conversation = await chat_service.get_conversation(chat_id, current_user.id)
        if not conversation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Conversation not found"
            )
        
        # Update the title
        updated_conversation = await chat_service.update_conversation_title(
            chat_id=chat_id,
            title=request.title
        )
        
        return {
            "success": True,
            "chat_id": chat_id,
            "title": updated_conversation.title,
            "message": "Chat title updated successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error updating chat title: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update chat title: {str(e)}"
        )


@router.delete("/conversations/{chat_id}")
async def delete_chat(
    chat_id: UUID,
    current_user: Account = Depends(get_current_user_from_jwt_required),
    db_session: Session = Depends(get_db_session),
):
    """
    Delete a chat conversation and all its messages.
    """
    try:
        chat_service = ChatService(db_session)
        
        # Verify user owns this conversation
        conversation = await chat_service.get_conversation(chat_id, current_user.id)
        if not conversation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Conversation not found"
            )
        
        # Delete the conversation
        await chat_service.delete_conversation(chat_id)
        
        return {
            "success": True,
            "chat_id": chat_id,
            "message": "Chat deleted successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Error deleting chat: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete chat: {str(e)}"
        )


# Legacy endpoints for backward compatibility
@router.post("/stream")
async def stream_chat(
    request: ChatRequest,
    current_user: Account = Depends(get_current_user_from_jwt_required),
    db_session: Session = Depends(get_db_session),
):
    """
    Legacy streaming chat endpoint.
    """
    return await create_chat(
        request=request,
        current_user=current_user,
        db_session=db_session,
        stream=True
    )


@router.post("/conversations/{chat_id}/stream")
async def stream_chat_with_id(
    chat_id: UUID,
    request: ChatRequest,
    current_user: Account = Depends(get_current_user_from_jwt_required),
    db_session: Session = Depends(get_db_session),
):
    """
    Legacy streaming chat endpoint with conversation ID.
    """
    return await create_chat(
        request=request,
        current_user=current_user,
        db_session=db_session,
        stream=True,
        chat_id=chat_id
    ) 