"""AI configuration settings."""

from pydantic import Field
from pydantic_settings import BaseSettings


class AIConfig(BaseSettings):
    """AI configuration settings."""
    
    # OpenAI Configuration
    OPENAI_API_KEY: str = Field(..., description="OpenAI API key")
    OPENAI_MODEL: str = Field(default="gpt-3.5-turbo", description="Default OpenAI model")
    OPENAI_MAX_TOKENS: int = Field(default=1000, description="Maximum tokens for OpenAI responses")
    OPENAI_TEMPERATURE: float = Field(default=0.7, description="Temperature for OpenAI responses")
    
    # LangChain Configuration
    LANGCHAIN_TRACING_V2: bool = Field(default=False, description="Enable LangChain tracing")
    LANGCHAIN_ENDPOINT: str = Field(default="https://api.smith.langchain.com", description="LangChain endpoint")
    LANGCHAIN_API_KEY: str = Field(default="", description="LangChain API key")
    LANGCHAIN_PROJECT: str = Field(default="", description="LangChain project name") 