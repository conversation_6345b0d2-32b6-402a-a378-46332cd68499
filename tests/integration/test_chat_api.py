"""Integration tests for chat API endpoints."""

import pytest
import json
from unittest.mock import patch, MagicMock, AsyncMock
from uuid import uuid4

from fastapi.testclient import TestClient
from sqlmodel import select

from src.entities.chat import ChatConversation, ChatMessage as ChatMessageEntity
from src.entities.account import Account
from src.entities.status import AccountStatus


@pytest.mark.integration
class TestChatAPIIntegration:
    """Integration tests for chat API."""

    def test_chat_endpoint_requires_authentication(self, test_client):
        """Test that chat endpoint requires authentication."""
        response = test_client.post(
            "/api/chat/",
            json={
                "messages": [{"role": "user", "content": "Hello"}],
                "model": "gpt-3.5-turbo"
            }
        )
        
        assert response.status_code == 401

    def test_create_new_chat_conversation(self, test_client, created_test_user, test_db_session):
        """Test creating a new chat conversation via API."""
        # Mock OpenAI response
        mock_openai_response = MagicMock()
        mock_openai_response.content = "Hello! How can I help you today?"
        
        with patch("src.services.chat_service.ChatOpenAI") as mock_openai_class:
            mock_openai_client = MagicMock()
            mock_openai_client.invoke = MagicMock(return_value=mock_openai_response)
            mock_openai_class.return_value = mock_openai_client
            
            response = test_client.post(
                "/api/chat/",
                headers={"Authorization": f"Bearer {created_test_user['access_token']}"},
                json={
                    "messages": [{"role": "user", "content": "Hello, how are you?"}],
                    "model": "gpt-3.5-turbo",
                    "temperature": 0.7,
                    "max_tokens": 1000
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            
            # Verify response structure
            assert "chat_id" in data
            assert "message" in data
            assert "success" in data
            assert data["success"] is True
            assert data["message"] == "Hello! How can I help you today?"
            
            # Verify conversation was created in database
            conversation = test_db_session.exec(
                select(ChatConversation).where(ChatConversation.id == data["chat_id"])
            ).first()
            
            assert conversation is not None
            assert conversation.user_id == created_test_user["id"]
            assert conversation.is_active is True
            
            # Verify messages were created
            messages = test_db_session.exec(
                select(ChatMessageEntity).where(ChatMessageEntity.conversation_id == conversation.id)
            ).all()
            
            assert len(messages) == 2  # User message + AI response
            assert messages[0].role == "user"
            assert messages[0].content == "Hello, how are you?"
            assert messages[1].role == "assistant"
            assert messages[1].content == "Hello! How can I help you today?"

    def test_continue_existing_chat_conversation(self, test_client, created_test_user, test_db_session):
        """Test continuing an existing chat conversation."""
        # First create a conversation
        mock_openai_response1 = MagicMock()
        mock_openai_response1.content = "Hello! How can I help you?"
        
        mock_openai_response2 = MagicMock()
        mock_openai_response2.content = "I'm doing well, thank you for asking!"
        
        with patch("src.services.chat_service.ChatOpenAI") as mock_openai_class:
            mock_openai_client = MagicMock()
            mock_openai_client.invoke = MagicMock(side_effect=[mock_openai_response1, mock_openai_response2])
            mock_openai_class.return_value = mock_openai_client
            
            # Create initial conversation
            response1 = test_client.post(
                "/api/chat/",
                headers={"Authorization": f"Bearer {created_test_user['access_token']}"},
                json={
                    "messages": [{"role": "user", "content": "Hello"}],
                    "model": "gpt-3.5-turbo"
                }
            )
            
            assert response1.status_code == 200
            chat_id = response1.json()["chat_id"]
            
            # Continue the conversation
            response2 = test_client.post(
                f"/api/chat/?chat_id={chat_id}",
                headers={"Authorization": f"Bearer {created_test_user['access_token']}"},
                json={
                    "messages": [{"role": "user", "content": "How are you doing?"}],
                    "model": "gpt-3.5-turbo"
                }
            )
            
            assert response2.status_code == 200
            data2 = response2.json()
            
            # Should use same chat_id
            assert data2["chat_id"] == chat_id
            assert data2["message"] == "I'm doing well, thank you for asking!"
            
            # Verify total messages in conversation
            messages = test_db_session.exec(
                select(ChatMessageEntity).where(ChatMessageEntity.conversation_id == chat_id)
            ).all()
            
            assert len(messages) == 4  # 2 user messages + 2 AI responses

    def test_chat_streaming_response(self, test_client, created_test_user):
        """Test streaming chat response."""
        # Mock streaming chunks
        mock_chunks = [
            MagicMock(content="Hello"),
            MagicMock(content=" there!"),
            MagicMock(content=" How"),
            MagicMock(content=" can I help?"),
            MagicMock(content="")
        ]
        
        async def mock_astream(messages):
            for chunk in mock_chunks:
                yield chunk
        
        with patch("src.services.chat_service.ChatOpenAI") as mock_openai_class:
            mock_openai_client = MagicMock()
            mock_openai_client.astream = mock_astream
            mock_openai_class.return_value = mock_openai_client
            
            response = test_client.post(
                "/api/chat/?stream=true",
                headers={"Authorization": f"Bearer {created_test_user['access_token']}"},
                json={
                    "messages": [{"role": "user", "content": "Hello"}],
                    "model": "gpt-3.5-turbo"
                }
            )
            
            assert response.status_code == 200
            assert response.headers["content-type"] == "text/plain; charset=utf-8"
            assert response.headers["cache-control"] == "no-cache"
            assert response.headers["connection"] == "keep-alive"
            
            # Verify streaming content
            content = response.text
            assert "data:" in content
            assert "Hello" in content
            assert "there!" in content

    def test_list_conversations(self, test_client, created_test_user, test_db_session):
        """Test listing user conversations."""
        # Create multiple conversations
        mock_openai_response = MagicMock()
        mock_openai_response.content = "Test response"
        
        with patch("src.services.chat_service.ChatOpenAI") as mock_openai_class:
            mock_openai_client = MagicMock()
            mock_openai_client.invoke = MagicMock(return_value=mock_openai_response)
            mock_openai_class.return_value = mock_openai_client
            
            # Create first conversation
            response1 = test_client.post(
                "/api/chat/",
                headers={"Authorization": f"Bearer {created_test_user['access_token']}"},
                json={
                    "messages": [{"role": "user", "content": "First conversation"}],
                    "model": "gpt-3.5-turbo"
                }
            )
            
            # Create second conversation
            response2 = test_client.post(
                "/api/chat/",
                headers={"Authorization": f"Bearer {created_test_user['access_token']}"},
                json={
                    "messages": [{"role": "user", "content": "Second conversation"}],
                    "model": "gpt-3.5-turbo"
                }
            )
            
            assert response1.status_code == 200
            assert response2.status_code == 200
            
            # List conversations
            list_response = test_client.get(
                "/api/chat/conversations",
                headers={"Authorization": f"Bearer {created_test_user['access_token']}"}
            )
            
            assert list_response.status_code == 200
            data = list_response.json()
            
            # Verify response structure
            assert "conversations" in data
            assert "total" in data
            assert "limit" in data
            assert "offset" in data
            
            assert data["total"] == 2
            assert len(data["conversations"]) == 2

    def test_list_conversations_pagination(self, test_client, created_test_user):
        """Test conversation listing with pagination."""
        # Create multiple conversations
        mock_openai_response = MagicMock()
        mock_openai_response.content = "Test response"
        
        with patch("src.services.chat_service.ChatOpenAI") as mock_openai_class:
            mock_openai_client = MagicMock()
            mock_openai_client.invoke = MagicMock(return_value=mock_openai_response)
            mock_openai_class.return_value = mock_openai_client
            
            # Create 5 conversations
            for i in range(5):
                test_client.post(
                    "/api/chat/",
                    headers={"Authorization": f"Bearer {created_test_user['access_token']}"},
                    json={
                        "messages": [{"role": "user", "content": f"Conversation {i}"}],
                        "model": "gpt-3.5-turbo"
                    }
                )
            
            # Test pagination
            page1_response = test_client.get(
                "/api/chat/conversations?limit=2&offset=0",
                headers={"Authorization": f"Bearer {created_test_user['access_token']}"}
            )
            
            page2_response = test_client.get(
                "/api/chat/conversations?limit=2&offset=2",
                headers={"Authorization": f"Bearer {created_test_user['access_token']}"}
            )
            
            assert page1_response.status_code == 200
            assert page2_response.status_code == 200
            
            page1_data = page1_response.json()
            page2_data = page2_response.json()
            
            # Verify pagination
            assert len(page1_data["conversations"]) == 2
            assert len(page2_data["conversations"]) == 2
            assert page1_data["limit"] == 2
            assert page1_data["offset"] == 0
            assert page2_data["limit"] == 2
            assert page2_data["offset"] == 2

    def test_chat_error_handling(self, test_client, created_test_user):
        """Test error handling in chat API."""
        # Test with invalid request data
        response = test_client.post(
            "/api/chat/",
            headers={"Authorization": f"Bearer {created_test_user['access_token']}"},
            json={
                "messages": [],  # Empty messages
                "model": "gpt-3.5-turbo"
            }
        )
        
        assert response.status_code == 422  # Validation error

    def test_chat_with_invalid_chat_id(self, test_client, created_test_user):
        """Test chat with non-existent chat ID."""
        fake_chat_id = str(uuid4())
        
        mock_openai_response = MagicMock()
        mock_openai_response.content = "Test response"
        
        with patch("src.services.chat_service.ChatOpenAI") as mock_openai_class:
            mock_openai_client = MagicMock()
            mock_openai_client.invoke = MagicMock(return_value=mock_openai_response)
            mock_openai_class.return_value = mock_openai_client
            
            response = test_client.post(
                f"/api/chat/?chat_id={fake_chat_id}",
                headers={"Authorization": f"Bearer {created_test_user['access_token']}"},
                json={
                    "messages": [{"role": "user", "content": "Hello"}],
                    "model": "gpt-3.5-turbo"
                }
            )
            
            # Should still work - will create new conversation if chat_id doesn't exist
            assert response.status_code == 200

    def test_unauthorized_access_to_other_user_conversation(self, test_client, created_test_user, test_db_session):
        """Test that users cannot access other users' conversations."""
        # This test would require creating another user and their conversation
        # For now, we'll test with a fake conversation ID
        fake_chat_id = str(uuid4())
        
        response = test_client.get(
            f"/api/chat/conversations/{fake_chat_id}/messages",
            headers={"Authorization": f"Bearer {created_test_user['access_token']}"}
        )
        
        # Should return 404 or 403 for non-existent/unauthorized conversation
        assert response.status_code in [404, 403, 500]
