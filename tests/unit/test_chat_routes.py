"""Unit tests for chat routes."""

import pytest
from unittest.mock import As<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, patch
from uuid import uuid4

from fastapi import HTTPException
from fastapi.responses import StreamingResponse

from src.routes.v1.chat import router, create_chat
from src.models.chat import Chat<PERSON><PERSON><PERSON>, ChatMessage, ChatCreateResponse
from src.entities.account import Account
from src.entities.status import AccountStatus


@pytest.mark.unit
class TestChatRoutes:
    """Test chat route functionality."""

    def test_chat_router_exists(self):
        """Test that chat router exists and has correct configuration."""
        assert router is not None
        assert router.prefix == "/api/chat"
        assert "Chat" in router.tags

    def test_chat_router_has_routes(self):
        """Test that chat router has expected routes."""
        route_paths = [route.path for route in router.routes]
        
        # Check main endpoints exist
        assert "/api/chat/" in route_paths
        assert "/api/chat/conversations" in route_paths
        assert "/api/chat/stream" in route_paths

    @pytest.mark.asyncio
    async def test_create_chat_function_exists(self):
        """Test that create_chat function exists and is callable."""
        assert create_chat is not None
        assert callable(create_chat)

    @pytest.mark.asyncio
    async def test_create_chat_with_new_conversation(self):
        """Test creating a new chat conversation."""
        # Mock dependencies
        mock_user = MagicMock(spec=Account)
        mock_user.id = uuid4()
        
        mock_db_session = MagicMock()
        
        mock_chat_service = MagicMock()
        mock_conversation = MagicMock()
        mock_conversation.id = uuid4()
        mock_chat_service.create_conversation = AsyncMock(return_value=mock_conversation)
        mock_chat_service.add_message = AsyncMock(return_value=MagicMock())
        mock_chat_service.generate_response = AsyncMock(return_value="Test AI response")
        
        # Create test request
        request = ChatRequest(
            messages=[ChatMessage(role="user", content="Hello, how are you?")],
            model="gpt-3.5-turbo"
        )
        
        with patch("src.routes.v1.chat.ChatService", return_value=mock_chat_service):
            result = await create_chat(
                request=request,
                current_user=mock_user,
                db_session=mock_db_session,
                stream=False,
                chat_id=None
            )
            
            # Verify result
            assert isinstance(result, ChatCreateResponse)
            assert result.chat_id == mock_conversation.id
            assert result.message == "Test AI response"
            assert result.success is True
            
            # Verify service calls
            mock_chat_service.create_conversation.assert_called_once()
            mock_chat_service.add_message.assert_called_once()
            mock_chat_service.generate_response.assert_called_once()

    @pytest.mark.asyncio
    async def test_create_chat_with_existing_conversation(self):
        """Test continuing an existing chat conversation."""
        # Mock dependencies
        mock_user = MagicMock(spec=Account)
        mock_user.id = uuid4()
        
        mock_db_session = MagicMock()
        existing_chat_id = uuid4()
        
        mock_chat_service = MagicMock()
        mock_chat_service.add_message = AsyncMock(return_value=MagicMock())
        mock_chat_service.generate_response = AsyncMock(return_value="Test AI response")
        
        # Create test request
        request = ChatRequest(
            messages=[ChatMessage(role="user", content="Continue our conversation")],
            model="gpt-3.5-turbo"
        )
        
        with patch("src.routes.v1.chat.ChatService", return_value=mock_chat_service):
            result = await create_chat(
                request=request,
                current_user=mock_user,
                db_session=mock_db_session,
                stream=False,
                chat_id=existing_chat_id
            )
            
            # Verify result
            assert isinstance(result, ChatCreateResponse)
            assert result.chat_id == existing_chat_id
            assert result.message == "Test AI response"
            assert result.success is True
            
            # Verify service calls - should not create new conversation
            mock_chat_service.create_conversation.assert_not_called()
            mock_chat_service.add_message.assert_called_once()
            mock_chat_service.generate_response.assert_called_once()

    @pytest.mark.asyncio
    async def test_create_chat_with_streaming(self):
        """Test creating a chat with streaming response."""
        # Mock dependencies
        mock_user = MagicMock(spec=Account)
        mock_user.id = uuid4()
        
        mock_db_session = MagicMock()
        
        mock_chat_service = MagicMock()
        mock_conversation = MagicMock()
        mock_conversation.id = uuid4()
        mock_chat_service.create_conversation = AsyncMock(return_value=mock_conversation)
        mock_chat_service.add_message = AsyncMock(return_value=MagicMock())
        
        # Mock streaming response
        async def mock_stream_response(*args, **kwargs):
            yield "data: {'content': 'Hello', 'finish_reason': null}\n\n"
            yield "data: {'content': ' there!', 'finish_reason': 'stop'}\n\n"
        
        mock_chat_service.stream_response = mock_stream_response
        
        # Create test request
        request = ChatRequest(
            messages=[ChatMessage(role="user", content="Hello, stream response please")],
            model="gpt-3.5-turbo"
        )
        
        with patch("src.routes.v1.chat.ChatService", return_value=mock_chat_service):
            result = await create_chat(
                request=request,
                current_user=mock_user,
                db_session=mock_db_session,
                stream=True,
                chat_id=None
            )
            
            # Verify result is StreamingResponse
            assert isinstance(result, StreamingResponse)
            assert result.media_type == "text/plain"
            assert result.headers["Cache-Control"] == "no-cache"
            assert result.headers["Connection"] == "keep-alive"
            
            # Verify service calls
            mock_chat_service.create_conversation.assert_called_once()
            mock_chat_service.add_message.assert_called_once()

    @pytest.mark.asyncio
    async def test_create_chat_error_handling(self):
        """Test error handling in create_chat function."""
        # Mock dependencies
        mock_user = MagicMock(spec=Account)
        mock_user.id = uuid4()

        mock_db_session = MagicMock()

        mock_chat_service = MagicMock()
        mock_chat_service.create_conversation = AsyncMock(side_effect=Exception("Database error"))

        # Create test request
        request = ChatRequest(
            messages=[ChatMessage(role="user", content="This should fail")],
            model="gpt-3.5-turbo"
        )

        with patch("src.routes.v1.chat.ChatService", return_value=mock_chat_service):
            with pytest.raises(HTTPException) as exc_info:
                await create_chat(
                    request=request,
                    current_user=mock_user,
                    db_session=mock_db_session,
                    stream=False,
                    chat_id=None
                )

            # Verify error details
            assert exc_info.value.status_code == 500
            assert "Chat error: Database error" in str(exc_info.value.detail)

    @pytest.mark.asyncio
    async def test_create_chat_with_empty_message(self):
        """Test creating chat with empty message content."""
        # Mock dependencies
        mock_user = MagicMock(spec=Account)
        mock_user.id = uuid4()

        mock_db_session = MagicMock()

        mock_chat_service = MagicMock()
        mock_conversation = MagicMock()
        mock_conversation.id = uuid4()
        mock_chat_service.create_conversation = AsyncMock(return_value=mock_conversation)
        mock_chat_service.add_message = AsyncMock(return_value=MagicMock())
        mock_chat_service.generate_response = AsyncMock(return_value="I understand you sent an empty message.")

        # Create test request with empty content
        request = ChatRequest(
            messages=[ChatMessage(role="user", content="")],
            model="gpt-3.5-turbo"
        )

        with patch("src.routes.v1.chat.ChatService", return_value=mock_chat_service):
            result = await create_chat(
                request=request,
                current_user=mock_user,
                db_session=mock_db_session,
                stream=False,
                chat_id=None
            )

            # Verify result
            assert isinstance(result, ChatCreateResponse)
            assert result.success is True

            # Verify service calls
            mock_chat_service.create_conversation.assert_called_once()
            mock_chat_service.add_message.assert_called_once()
            mock_chat_service.generate_response.assert_called_once()

    @pytest.mark.asyncio
    async def test_create_chat_with_long_message(self):
        """Test creating chat with very long message content."""
        # Mock dependencies
        mock_user = MagicMock(spec=Account)
        mock_user.id = uuid4()

        mock_db_session = MagicMock()

        mock_chat_service = MagicMock()
        mock_conversation = MagicMock()
        mock_conversation.id = uuid4()
        mock_chat_service.create_conversation = AsyncMock(return_value=mock_conversation)
        mock_chat_service.add_message = AsyncMock(return_value=MagicMock())
        mock_chat_service.generate_response = AsyncMock(return_value="I received your long message.")

        # Create test request with very long content
        long_content = "This is a very long message. " * 100  # 3000+ characters
        request = ChatRequest(
            messages=[ChatMessage(role="user", content=long_content)],
            model="gpt-3.5-turbo"
        )

        with patch("src.routes.v1.chat.ChatService", return_value=mock_chat_service):
            result = await create_chat(
                request=request,
                current_user=mock_user,
                db_session=mock_db_session,
                stream=False,
                chat_id=None
            )

            # Verify result
            assert isinstance(result, ChatCreateResponse)
            assert result.success is True

            # Verify conversation title is truncated
            create_call_args = mock_chat_service.create_conversation.call_args
            title_arg = create_call_args.kwargs.get('title', '')
            assert len(title_arg) <= 53  # 50 chars + "..."

    @pytest.mark.asyncio
    async def test_create_chat_with_custom_parameters(self):
        """Test creating chat with custom model parameters."""
        # Mock dependencies
        mock_user = MagicMock(spec=Account)
        mock_user.id = uuid4()

        mock_db_session = MagicMock()

        mock_chat_service = MagicMock()
        mock_conversation = MagicMock()
        mock_conversation.id = uuid4()
        mock_chat_service.create_conversation = AsyncMock(return_value=mock_conversation)
        mock_chat_service.add_message = AsyncMock(return_value=MagicMock())
        mock_chat_service.generate_response = AsyncMock(return_value="Custom response")

        # Create test request with custom parameters
        request = ChatRequest(
            messages=[ChatMessage(role="user", content="Test with custom params")],
            model="gpt-4",
            temperature=0.9,
            max_tokens=2000
        )

        with patch("src.routes.v1.chat.ChatService", return_value=mock_chat_service):
            result = await create_chat(
                request=request,
                current_user=mock_user,
                db_session=mock_db_session,
                stream=False,
                chat_id=None
            )

            # Verify result
            assert isinstance(result, ChatCreateResponse)
            assert result.success is True

            # Verify service calls with custom parameters
            generate_call_args = mock_chat_service.generate_response.call_args
            assert generate_call_args.kwargs['model'] == "gpt-4"
            assert generate_call_args.kwargs['temperature'] == 0.9
            assert generate_call_args.kwargs['max_tokens'] == 2000
