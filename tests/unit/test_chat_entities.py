"""Unit tests for chat database entities."""

import pytest
from datetime import datetime
from uuid import uuid4

from sqlmodel import Session, select

from src.entities.chat import Chat<PERSON>onversation, ChatMessage
from src.entities.account import Account
from src.entities.status import AccountStatus


@pytest.mark.unit
class TestChatConversation:
    """Test ChatConversation entity."""

    def test_chat_conversation_creation(self, test_db_session):
        """Test creating a new chat conversation."""
        user_id = uuid4()
        title = "Test Conversation"
        
        conversation = ChatConversation(
            user_id=user_id,
            title=title
        )
        
        test_db_session.add(conversation)
        test_db_session.commit()
        test_db_session.refresh(conversation)
        
        # Verify conversation was created
        assert conversation.id is not None
        assert conversation.user_id == user_id
        assert conversation.title == title
        assert conversation.is_active is True
        assert isinstance(conversation.created_at, datetime)
        assert isinstance(conversation.updated_at, datetime)

    def test_chat_conversation_default_values(self, test_db_session):
        """Test default values for chat conversation."""
        user_id = uuid4()
        
        conversation = ChatConversation(
            user_id=user_id,
            title="Test"
        )
        
        test_db_session.add(conversation)
        test_db_session.commit()
        test_db_session.refresh(conversation)
        
        # Verify default values
        assert conversation.is_active is True
        assert conversation.created_at is not None
        assert conversation.updated_at is not None

    def test_chat_conversation_update(self, test_db_session):
        """Test updating a chat conversation."""
        user_id = uuid4()
        original_title = "Original Title"
        new_title = "Updated Title"
        
        conversation = ChatConversation(
            user_id=user_id,
            title=original_title
        )
        
        test_db_session.add(conversation)
        test_db_session.commit()
        test_db_session.refresh(conversation)
        
        original_updated_at = conversation.updated_at
        
        # Update the conversation
        conversation.title = new_title
        conversation.updated_at = datetime.utcnow()
        
        test_db_session.add(conversation)
        test_db_session.commit()
        test_db_session.refresh(conversation)
        
        # Verify update
        assert conversation.title == new_title
        assert conversation.updated_at > original_updated_at

    def test_chat_conversation_soft_delete(self, test_db_session):
        """Test soft delete functionality."""
        user_id = uuid4()
        
        conversation = ChatConversation(
            user_id=user_id,
            title="To be deleted"
        )
        
        test_db_session.add(conversation)
        test_db_session.commit()
        test_db_session.refresh(conversation)
        
        # Soft delete
        conversation.is_active = False
        test_db_session.add(conversation)
        test_db_session.commit()
        test_db_session.refresh(conversation)
        
        # Verify soft delete
        assert conversation.is_active is False
        assert conversation.id is not None  # Still exists in database

    def test_chat_conversation_query_active(self, test_db_session):
        """Test querying only active conversations."""
        user_id = uuid4()
        
        # Create active conversation
        active_conv = ChatConversation(
            user_id=user_id,
            title="Active Conversation",
            is_active=True
        )
        
        # Create inactive conversation
        inactive_conv = ChatConversation(
            user_id=user_id,
            title="Inactive Conversation",
            is_active=False
        )
        
        test_db_session.add(active_conv)
        test_db_session.add(inactive_conv)
        test_db_session.commit()
        
        # Query only active conversations
        active_conversations = test_db_session.exec(
            select(ChatConversation).where(
                ChatConversation.user_id == user_id,
                ChatConversation.is_active == True
            )
        ).all()
        
        # Verify only active conversation is returned
        assert len(active_conversations) == 1
        assert active_conversations[0].title == "Active Conversation"

    def test_chat_conversation_user_filtering(self, test_db_session):
        """Test filtering conversations by user."""
        user1_id = uuid4()
        user2_id = uuid4()
        
        # Create conversations for different users
        conv1 = ChatConversation(user_id=user1_id, title="User 1 Conversation")
        conv2 = ChatConversation(user_id=user2_id, title="User 2 Conversation")
        conv3 = ChatConversation(user_id=user1_id, title="Another User 1 Conversation")
        
        test_db_session.add_all([conv1, conv2, conv3])
        test_db_session.commit()
        
        # Query conversations for user1
        user1_conversations = test_db_session.exec(
            select(ChatConversation).where(ChatConversation.user_id == user1_id)
        ).all()
        
        # Verify filtering
        assert len(user1_conversations) == 2
        for conv in user1_conversations:
            assert conv.user_id == user1_id


@pytest.mark.unit
class TestChatMessage:
    """Test ChatMessage entity."""

    def test_chat_message_creation(self, test_db_session):
        """Test creating a new chat message."""
        # First create a conversation
        user_id = uuid4()
        conversation = ChatConversation(user_id=user_id, title="Test Conversation")
        test_db_session.add(conversation)
        test_db_session.commit()
        test_db_session.refresh(conversation)
        
        # Create message
        message = ChatMessage(
            conversation_id=conversation.id,
            role="user",
            content="Hello, how are you?",
            model="gpt-3.5-turbo",
            tokens_used=10
        )
        
        test_db_session.add(message)
        test_db_session.commit()
        test_db_session.refresh(message)
        
        # Verify message was created
        assert message.id is not None
        assert message.conversation_id == conversation.id
        assert message.role == "user"
        assert message.content == "Hello, how are you?"
        assert message.model == "gpt-3.5-turbo"
        assert message.tokens_used == 10
        assert isinstance(message.created_at, datetime)

    def test_chat_message_default_values(self, test_db_session):
        """Test default values for chat message."""
        # Create conversation first
        user_id = uuid4()
        conversation = ChatConversation(user_id=user_id, title="Test")
        test_db_session.add(conversation)
        test_db_session.commit()
        test_db_session.refresh(conversation)
        
        # Create message with minimal data
        message = ChatMessage(
            conversation_id=conversation.id,
            role="assistant",
            content="Test response"
        )
        
        test_db_session.add(message)
        test_db_session.commit()
        test_db_session.refresh(message)
        
        # Verify default values
        assert message.model is None
        assert message.tokens_used is None
        assert message.created_at is not None

    def test_chat_message_conversation_relationship(self, test_db_session):
        """Test relationship between messages and conversations."""
        # Create conversation
        user_id = uuid4()
        conversation = ChatConversation(user_id=user_id, title="Test Conversation")
        test_db_session.add(conversation)
        test_db_session.commit()
        test_db_session.refresh(conversation)
        
        # Create multiple messages
        messages = [
            ChatMessage(
                conversation_id=conversation.id,
                role="user",
                content="First message"
            ),
            ChatMessage(
                conversation_id=conversation.id,
                role="assistant",
                content="First response"
            ),
            ChatMessage(
                conversation_id=conversation.id,
                role="user",
                content="Second message"
            )
        ]
        
        test_db_session.add_all(messages)
        test_db_session.commit()
        
        # Query messages for conversation
        conversation_messages = test_db_session.exec(
            select(ChatMessage).where(ChatMessage.conversation_id == conversation.id)
        ).all()
        
        # Verify relationship
        assert len(conversation_messages) == 3
        for msg in conversation_messages:
            assert msg.conversation_id == conversation.id

    def test_chat_message_ordering(self, test_db_session):
        """Test ordering messages by creation time."""
        # Create conversation
        user_id = uuid4()
        conversation = ChatConversation(user_id=user_id, title="Test")
        test_db_session.add(conversation)
        test_db_session.commit()
        test_db_session.refresh(conversation)
        
        # Create messages with slight time differences
        import time
        
        msg1 = ChatMessage(conversation_id=conversation.id, role="user", content="First")
        test_db_session.add(msg1)
        test_db_session.commit()
        
        time.sleep(0.01)  # Small delay
        
        msg2 = ChatMessage(conversation_id=conversation.id, role="assistant", content="Second")
        test_db_session.add(msg2)
        test_db_session.commit()
        
        time.sleep(0.01)  # Small delay
        
        msg3 = ChatMessage(conversation_id=conversation.id, role="user", content="Third")
        test_db_session.add(msg3)
        test_db_session.commit()
        
        # Query messages ordered by creation time
        ordered_messages = test_db_session.exec(
            select(ChatMessage)
            .where(ChatMessage.conversation_id == conversation.id)
            .order_by(ChatMessage.created_at)
        ).all()
        
        # Verify ordering
        assert len(ordered_messages) == 3
        assert ordered_messages[0].content == "First"
        assert ordered_messages[1].content == "Second"
        assert ordered_messages[2].content == "Third"
