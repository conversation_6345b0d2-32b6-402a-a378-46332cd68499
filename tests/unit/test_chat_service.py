"""Unit tests for chat service."""

import pytest
from unittest.mock import As<PERSON><PERSON><PERSON>, MagicMock, patch
from uuid import uuid4

from src.services.chat_service import ChatService
from src.entities.chat import Chat<PERSON>onversation, ChatMessage as ChatMessageEntity
from src.models.chat import ChatMessage, ChatStreamResponse


@pytest.mark.unit
class TestChatService:
    """Test ChatService functionality."""

    def test_chat_service_initialization(self, test_db_session):
        """Test ChatService initialization."""
        with patch("src.services.chat_service.ChatOpenAI") as mock_openai:
            service = ChatService(test_db_session)
            
            assert service.db_session == test_db_session
            assert service.openai_client is not None
            mock_openai.assert_called_once()

    @pytest.mark.asyncio
    async def test_create_conversation(self, test_db_session):
        """Test creating a new conversation."""
        with patch("src.services.chat_service.ChatOpenAI"):
            service = ChatService(test_db_session)
            
            user_id = uuid4()
            title = "Test Conversation"
            
            conversation = await service.create_conversation(user_id, title)
            
            # Verify conversation was created
            assert isinstance(conversation, ChatConversation)
            assert conversation.user_id == user_id
            assert conversation.title == title
            assert conversation.is_active is True
            assert conversation.id is not None

    @pytest.mark.asyncio
    async def test_create_conversation_error_handling(self, test_db_session):
        """Test error handling in create_conversation."""
        with patch("src.services.chat_service.ChatOpenAI"):
            service = ChatService(test_db_session)
            
            # Mock database error
            with patch.object(test_db_session, 'add', side_effect=Exception("DB Error")):
                with pytest.raises(Exception) as exc_info:
                    await service.create_conversation(uuid4(), "Test")
                
                assert "DB Error" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_add_message(self, test_db_session):
        """Test adding a message to a conversation."""
        with patch("src.services.chat_service.ChatOpenAI"):
            service = ChatService(test_db_session)
            
            # Create conversation first
            user_id = uuid4()
            conversation = await service.create_conversation(user_id, "Test")
            
            # Add message
            message = await service.add_message(
                conversation_id=conversation.id,
                user_id=user_id,
                role="user",
                content="Hello!",
                model="gpt-3.5-turbo"
            )
            
            # Verify message was added
            assert isinstance(message, ChatMessageEntity)
            assert message.conversation_id == conversation.id
            assert message.role == "user"
            assert message.content == "Hello!"
            assert message.model == "gpt-3.5-turbo"
            assert message.id is not None

    @pytest.mark.asyncio
    async def test_add_message_updates_conversation_timestamp(self, test_db_session):
        """Test that adding a message updates conversation timestamp."""
        with patch("src.services.chat_service.ChatOpenAI"):
            service = ChatService(test_db_session)
            
            # Create conversation
            user_id = uuid4()
            conversation = await service.create_conversation(user_id, "Test")
            original_updated_at = conversation.updated_at
            
            # Add message
            await service.add_message(
                conversation_id=conversation.id,
                user_id=user_id,
                role="user",
                content="Hello!"
            )
            
            # Refresh conversation and check timestamp
            test_db_session.refresh(conversation)
            assert conversation.updated_at > original_updated_at

    @pytest.mark.asyncio
    async def test_get_conversation(self, test_db_session):
        """Test retrieving a conversation."""
        with patch("src.services.chat_service.ChatOpenAI"):
            service = ChatService(test_db_session)
            
            # Create conversation
            user_id = uuid4()
            conversation = await service.create_conversation(user_id, "Test")
            
            # Retrieve conversation
            retrieved = await service.get_conversation(conversation.id, user_id)
            
            # Verify retrieval
            assert retrieved is not None
            assert retrieved.id == conversation.id
            assert retrieved.user_id == user_id

    @pytest.mark.asyncio
    async def test_get_conversation_wrong_user(self, test_db_session):
        """Test retrieving conversation with wrong user ID."""
        with patch("src.services.chat_service.ChatOpenAI"):
            service = ChatService(test_db_session)
            
            # Create conversation
            user_id = uuid4()
            wrong_user_id = uuid4()
            conversation = await service.create_conversation(user_id, "Test")
            
            # Try to retrieve with wrong user ID
            retrieved = await service.get_conversation(conversation.id, wrong_user_id)
            
            # Should return None
            assert retrieved is None

    @pytest.mark.asyncio
    async def test_get_user_conversations(self, test_db_session):
        """Test retrieving user conversations."""
        with patch("src.services.chat_service.ChatOpenAI"):
            service = ChatService(test_db_session)
            
            user_id = uuid4()
            other_user_id = uuid4()
            
            # Create conversations for user
            conv1 = await service.create_conversation(user_id, "Conversation 1")
            conv2 = await service.create_conversation(user_id, "Conversation 2")
            
            # Create conversation for other user
            await service.create_conversation(other_user_id, "Other User Conv")
            
            # Retrieve user conversations
            conversations = await service.get_user_conversations(user_id, limit=10, offset=0)
            
            # Verify results
            assert len(conversations) == 2
            conversation_ids = [conv['id'] for conv in conversations]
            assert conv1.id in conversation_ids
            assert conv2.id in conversation_ids

    @pytest.mark.asyncio
    async def test_get_user_conversations_pagination(self, test_db_session):
        """Test pagination in get_user_conversations."""
        with patch("src.services.chat_service.ChatOpenAI"):
            service = ChatService(test_db_session)
            
            user_id = uuid4()
            
            # Create multiple conversations
            for i in range(5):
                await service.create_conversation(user_id, f"Conversation {i}")
            
            # Test pagination
            page1 = await service.get_user_conversations(user_id, limit=2, offset=0)
            page2 = await service.get_user_conversations(user_id, limit=2, offset=2)
            
            # Verify pagination
            assert len(page1) == 2
            assert len(page2) == 2
            
            # Verify different conversations
            page1_ids = [conv['id'] for conv in page1]
            page2_ids = [conv['id'] for conv in page2]
            assert set(page1_ids).isdisjoint(set(page2_ids))

    @pytest.mark.asyncio
    async def test_generate_response(self, test_db_session):
        """Test generating AI response."""
        mock_openai_response = MagicMock()
        mock_openai_response.content = "This is a test AI response."
        
        with patch("src.services.chat_service.ChatOpenAI") as mock_openai_class:
            mock_openai_client = MagicMock()
            mock_openai_client.invoke = MagicMock(return_value=mock_openai_response)
            mock_openai_class.return_value = mock_openai_client
            
            service = ChatService(test_db_session)
            
            # Create conversation and add user message
            user_id = uuid4()
            conversation = await service.create_conversation(user_id, "Test")
            await service.add_message(conversation.id, user_id, "user", "Hello!")
            
            # Generate response
            messages = [ChatMessage(role="user", content="Hello!")]
            response = await service.generate_response(
                conversation_id=conversation.id,
                user_id=user_id,
                messages=messages,
                model="gpt-3.5-turbo"
            )
            
            # Verify response
            assert response == "This is a test AI response."
            mock_openai_client.invoke.assert_called_once()

    @pytest.mark.asyncio
    async def test_stream_response(self, test_db_session):
        """Test streaming AI response."""
        # Mock streaming chunks
        mock_chunks = [
            MagicMock(content="Hello"),
            MagicMock(content=" there!"),
            MagicMock(content="")
        ]
        
        async def mock_astream(messages):
            for chunk in mock_chunks:
                yield chunk
        
        with patch("src.services.chat_service.ChatOpenAI") as mock_openai_class:
            mock_openai_client = MagicMock()
            mock_openai_client.astream = mock_astream
            mock_openai_class.return_value = mock_openai_client
            
            service = ChatService(test_db_session)
            
            # Create conversation and add user message
            user_id = uuid4()
            conversation = await service.create_conversation(user_id, "Test")
            await service.add_message(conversation.id, user_id, "user", "Hello!")
            
            # Stream response
            messages = [ChatMessage(role="user", content="Hello!")]
            stream_generator = service.stream_response(
                conversation_id=conversation.id,
                user_id=user_id,
                messages=messages,
                model="gpt-3.5-turbo"
            )
            
            # Collect streamed chunks
            chunks = []
            async for chunk in stream_generator:
                chunks.append(chunk)
            
            # Verify streaming
            assert len(chunks) > 0
            # Should have content chunks and end signal
            content_chunks = [chunk for chunk in chunks if "Hello" in chunk or "there!" in chunk]
            assert len(content_chunks) >= 2

    @pytest.mark.asyncio
    async def test_delete_conversation(self, test_db_session):
        """Test deleting a conversation."""
        with patch("src.services.chat_service.ChatOpenAI"):
            service = ChatService(test_db_session)

            # Create conversation
            user_id = uuid4()
            conversation = await service.create_conversation(user_id, "Test")

            # Add a message to the conversation
            await service.add_message(conversation.id, user_id, "user", "Test message")

            # Delete conversation
            await service.delete_conversation(conversation.id)

            # Verify conversation is deleted from database
            from sqlmodel import select
            from src.entities.chat import ChatConversation

            deleted_conv = test_db_session.exec(
                select(ChatConversation).where(ChatConversation.id == conversation.id)
            ).first()

            assert deleted_conv is None

    @pytest.mark.asyncio
    async def test_delete_conversation_with_messages(self, test_db_session):
        """Test deleting conversation also deletes associated messages."""
        with patch("src.services.chat_service.ChatOpenAI"):
            service = ChatService(test_db_session)

            # Create conversation
            user_id = uuid4()
            conversation = await service.create_conversation(user_id, "Test")

            # Add multiple messages
            await service.add_message(conversation.id, user_id, "user", "Message 1")
            await service.add_message(conversation.id, user_id, "assistant", "Response 1")
            await service.add_message(conversation.id, user_id, "user", "Message 2")

            # Delete conversation
            await service.delete_conversation(conversation.id)

            # Verify messages are also deleted
            from sqlmodel import select
            from src.entities.chat import ChatMessage as ChatMessageEntity

            remaining_messages = test_db_session.exec(
                select(ChatMessageEntity).where(ChatMessageEntity.conversation_id == conversation.id)
            ).all()

            assert len(remaining_messages) == 0
