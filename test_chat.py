#!/usr/bin/env python3
"""
Test script for Chat API functionality.
This script demonstrates how to use the chat endpoints.
"""

import asyncio
import json
import httpx
from typing import List


class ChatAPITester:
    """Test class for Chat API endpoints."""
    
    def __init__(self, base_url: str = "http://localhost:5001"):
        self.base_url = base_url
        self.client = httpx.AsyncClient()
        self.auth_token = None
    
    async def login(self, email: str, password: str) -> bool:
        """Login and get authentication token."""
        try:
            response = await self.client.post(
                f"{self.base_url}/api/v1/auth/login",
                json={
                    "email": email,
                    "password": password
                }
            )
            
            if response.status_code == 200:
                data = response.json()
                self.auth_token = data.get("access_token")
                print(f"✅ Login successful for {email}")
                return True
            else:
                print(f"❌ Login failed: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Login error: {e}")
            return False
    
    def get_headers(self) -> dict:
        """Get headers with authentication token."""
        headers = {"Content-Type": "application/json"}
        if self.auth_token:
            headers["Authorization"] = f"Bearer {self.auth_token}"
        return headers
    
    async def create_conversation(self, title: str) -> dict:
        """Create a new conversation."""
        try:
            response = await self.client.post(
                f"{self.base_url}/api/v1/chat/conversations",
                headers=self.get_headers(),
                params={"title": title}
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Conversation created: {data}")
                return data
            else:
                print(f"❌ Failed to create conversation: {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ Error creating conversation: {e}")
            return None
    
    async def list_conversations(self) -> List[dict]:
        """List all conversations."""
        try:
            response = await self.client.get(
                f"{self.base_url}/api/v1/chat/conversations",
                headers=self.get_headers()
            )
            
            if response.status_code == 200:
                conversations = response.json()
                print(f"✅ Found {len(conversations)} conversations")
                for conv in conversations:
                    print(f"  - {conv['title']} (ID: {conv['id']})")
                return conversations
            else:
                print(f"❌ Failed to list conversations: {response.text}")
                return []
                
        except Exception as e:
            print(f"❌ Error listing conversations: {e}")
            return []
    
    async def send_message(self, conversation_id: int, message: str, model: str = "gpt-3.5-turbo") -> dict:
        """Send a message to a conversation."""
        try:
            payload = {
                "messages": [
                    {"role": "user", "content": message}
                ],
                "model": model,
                "temperature": 0.7,
                "stream": False
            }
            
            response = await self.client.post(
                f"{self.base_url}/api/v1/chat/conversations/{conversation_id}/messages",
                headers=self.get_headers(),
                json=payload
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ AI Response: {data['message']}")
                return data
            else:
                print(f"❌ Failed to send message: {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ Error sending message: {e}")
            return None
    
    async def stream_message(self, conversation_id: int, message: str, model: str = "gpt-3.5-turbo"):
        """Stream a message response."""
        try:
            payload = {
                "messages": [
                    {"role": "user", "content": message}
                ],
                "model": model,
                "temperature": 0.7,
                "stream": True
            }
            
            async with self.client.stream(
                "POST",
                f"{self.base_url}/api/v1/chat/conversations/{conversation_id}/stream",
                headers=self.get_headers(),
                json=payload
            ) as response:
                
                if response.status_code == 200:
                    print("🔄 Streaming response:")
                    async for line in response.aiter_lines():
                        if line.startswith("data: "):
                            data_str = line[6:]  # Remove "data: " prefix
                            try:
                                data = json.loads(data_str)
                                if data.get("content"):
                                    print(data["content"], end="", flush=True)
                                if data.get("finish_reason") == "stop":
                                    print("\n✅ Streaming completed")
                                    break
                            except json.JSONDecodeError:
                                continue
                else:
                    print(f"❌ Failed to stream message: {response.text}")
                    
        except Exception as e:
            print(f"❌ Error streaming message: {e}")
    
    async def get_conversation_messages(self, conversation_id: int) -> List[dict]:
        """Get messages from a conversation."""
        try:
            response = await self.client.get(
                f"{self.base_url}/api/v1/chat/conversations/{conversation_id}/messages",
                headers=self.get_headers()
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Found {len(data['messages'])} messages in conversation")
                for msg in data['messages']:
                    print(f"  {msg['role']}: {msg['content'][:100]}...")
                return data['messages']
            else:
                print(f"❌ Failed to get messages: {response.text}")
                return []
                
        except Exception as e:
            print(f"❌ Error getting messages: {e}")
            return []
    
    async def close(self):
        """Close the HTTP client."""
        await self.client.aclose()


async def main():
    """Main test function."""
    print("🚀 Starting Chat API Test")
    print("=" * 50)
    
    # Initialize tester
    tester = ChatAPITester()
    
    try:
        # Login (you'll need to create an admin user first)
        print("\n1. Logging in...")
        success = await tester.login("<EMAIL>", "AdminPass123!")
        
        if not success:
            print("❌ Please create an admin user first using: uv run python command.py create-admin")
            return
        
        # Create a conversation
        print("\n2. Creating conversation...")
        conversation = await tester.create_conversation("Test Chat Session")
        if not conversation:
            return
        
        conversation_id = conversation['id']
        
        # List conversations
        print("\n3. Listing conversations...")
        await tester.list_conversations()
        
        # Send a message (non-streaming)
        print("\n4. Sending message (non-streaming)...")
        await tester.send_message(
            conversation_id, 
            "Hello! Can you tell me about Python programming?"
        )
        
        # Send a message (streaming)
        print("\n5. Sending message (streaming)...")
        await tester.stream_message(
            conversation_id,
            "What are the benefits of using FastAPI?"
        )
        
        # Get conversation messages
        print("\n6. Getting conversation messages...")
        await tester.get_conversation_messages(conversation_id)
        
        print("\n✅ Chat API test completed successfully!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
    
    finally:
        await tester.close()


if __name__ == "__main__":
    asyncio.run(main()) 